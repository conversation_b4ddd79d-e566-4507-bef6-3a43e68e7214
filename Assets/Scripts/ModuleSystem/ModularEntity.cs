using ModuleSystem.Modules.Base;
using PurrNet;
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedMember.Global
// ReSharper disable VirtualMemberNeverOverridden.Global
// ReSharper disable MemberCanBeProtected.Global

namespace ModuleSystem
{
    public abstract class ModularEntity : NetworkBehaviour
    {
        public SyncHashSet<Module> Modules;
        
        protected virtual void Start()
        {
            AddDefaultModules();
        }

        protected override void OnInitializeModules()
        {
            base.OnInitializeModules();
        }

        protected virtual void AddDefaultModules()
        {
            AddModule(new BaseInfoModule());
        }
        
        public void AddModule(Module module)
        {
            Modules.Add(module);
            module.Added(this);
        }

        public void RemoveModule(Module module)
        {
            Modules.Remove(module);
            module.Removed(this);
        }
    }
}